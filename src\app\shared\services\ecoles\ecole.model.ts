export interface Ecole {
    id?: number;
    nom?: string;
    subtitle?: string;
    pays?: number;
    addresse?: string;
    presentation?: string;
    logoUrl?: string;
    coverUrl?: string;
    isVerified?: boolean;
    isfirstChoice?: boolean;
    deleted?: boolean;
    
    ecoleCampuses: EcoleCampus[];
    ecoleFormations: EcoleFormation[];
    ecoleProgrammes: EcoleProgramme[];
}

export interface Setting {
    value?: number;
    text?: string;
}

export interface EcoleCampus {
    id?: number;
    nom?: string;
    pays?: number;
    addresse?: string;
}

export interface EcoleFormation {
    id?: number;
    idFormation?: number;
}

export interface EcoleProgramme {
    id?: number;
    nom?: string;
    diplome?: number;
    langue?: number;
    duree?: string;
    niveauxAdmission?: number[];
    fraisInscription?: string;
    fraisScholarite?: string;
    dateEntree?: string;
}