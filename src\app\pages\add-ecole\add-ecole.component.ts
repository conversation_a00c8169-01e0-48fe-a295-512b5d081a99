import { Component } from '@angular/core';
import { Router } from '@angular/router';
import notify from 'devextreme/ui/notify';
import { Ecole, EcoleCampus } from 'src/app/shared/services/ecoles/ecole.model';
import { EcoleService } from 'src/app/shared/services/ecoles/ecoles.service';
import { SettingsService } from 'src/app/shared/services/settings/settings.service';
import { ImageService } from 'src/app/shared/services/image/image.service';

@Component({
  templateUrl: 'add-ecole.component.html',
  styleUrls: [ './add-ecole.component.scss' ]
})

export class AddEcoleComponent {
  formData: Ecole = {
    nom: '',
    subtitle: '',
    pays: 0,
    addresse: '',
    logoUrl: '',
    coverUrl: '',
    isVerified: false,
    isfirstChoice: false,
    presentation: '',
    ecoleCampuses: [],
    ecoleFormations: [],
    ecoleProgrammes: []
  };
  
  campuses: EcoleCampus[] = [];
  programmes: any[] = [];
  
  diplomes: any[] = [];
  langues: any[] = [];
  formations: any[] = [];
  countries: any[] = [];
  niveauxScolaires: any[] = [];

  // Image upload states
  logoUploadProgress: number = 0;
  coverUploadProgress: number = 0;
  isUploadingLogo: boolean = false;
  isUploadingCover: boolean = false;

  constructor(
    private ecoleService: EcoleService,
    private settingsService: SettingsService,
    private imageService: ImageService,
    private router: Router
  ) {}

  async ngOnInit() {
    await this.loadData();
  }

  async submit() {
    try {
      const payload: Ecole = {
        ...this.formData,
        ecoleCampuses: this.campuses,
        ecoleProgrammes: this.programmes
      };
      console.log("🚀 ~ AddEcoleComponent ~ submit ~ payload:", payload)
      await this.ecoleService.createEcole(payload);
      notify('Ecole added successfully', 'success');
      // this.router.navigate(['/ecoles']);
    } catch (error) {
      notify('Error adding ecole', 'error');
      console.error('Submit error:', error);
    }
  }

  onCancel() {
    this.router.navigate(['/ecoles']);
  }

  isFormValid(): boolean {
    return this.formData.nom?.trim() !== '' &&
           this.formData.pays !== 0 &&
           this.campuses.length > 0 &&
           this.formations.length > 0 &&
           this.programmes.length > 0;
  }

  // Image upload handlers
  async onLogoSelect(event: any) {
    const file = event.value[0];
    if (file) {
      await this.uploadImage(file, 'logo');
    }
  }

  async onCoverSelect(event: any) {
    const file = event.value[0];
    if (file) {
      await this.uploadImage(file, 'cover');
    }
  }

  private async uploadImage(file: File, type: 'logo' | 'cover') {
    try {
      if (type === 'logo') {
        this.isUploadingLogo = true;
        this.logoUploadProgress = 0;
      } else {
        this.isUploadingCover = true;
        this.coverUploadProgress = 0;
      }

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('image', file);
      formData.append('type', type);

      // Upload with progress tracking
      const response = await this.imageService.uploadImage(formData, (progress: number) => {
        if (type === 'logo') {
          this.logoUploadProgress = progress;
        } else {
          this.coverUploadProgress = progress;
        }
      });

      // Update form data with the returned URL
      if (type === 'logo') {
        this.formData.logoUrl = response.url;
        notify('Logo uploaded successfully', 'success');
      } else {
        this.formData.coverUrl = response.url;
        notify('Cover image uploaded successfully', 'success');
      }

    } catch (error) {
      notify(`Error uploading ${type} image`, 'error');
      console.error(`${type} upload error:`, error);
    } finally {
      if (type === 'logo') {
        this.isUploadingLogo = false;
        this.logoUploadProgress = 0;
      } else {
        this.isUploadingCover = false;
        this.coverUploadProgress = 0;
      }
    }
  }

  // Remove uploaded images
  removeLogo() {
    this.formData.logoUrl = '';
    notify('Logo removed', 'info');
  }

  removeCover() {
    this.formData.coverUrl = '';
    notify('Cover image removed', 'info');
  }

  async loadData() {
    // Dummy data
    this.countries = [
      { value: 1, text: 'France' },
      { value: 2, text: 'Morocco' },
      { value: 3, text: 'USA' }
    ];
    this.diplomes = [
      { value: 1, text: 'Licence' },
      { value: 2, text: 'Master' },
      { value: 3, text: 'Doctorat' }
    ];
    this.formations = [
      { value: 1, text: 'Informatique' },
      { value: 2, text: 'Gestion' },
      { value: 3, text: 'Droit' }
    ];
    this.langues = [
      { value: 1, text: 'Français' },
      { value: 2, text: 'Anglais' },
      { value: 3, text: 'Arabe' }
    ];
    this.niveauxScolaires = [
      { value: 1, text: 'Bac' },
      { value: 2, text: 'Bac+2' },
      { value: 3, text: 'Bac+3' }
    ];
  }
}