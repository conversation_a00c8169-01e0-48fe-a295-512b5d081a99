import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { User } from './user.model';
import { firstValueFrom } from 'rxjs';
import { ApiClient } from '../api-client';
import { environment } from 'src/environments/environment';

const defaultPath = '/';

@Injectable()
export class AuthService {
  private MAIN_ENDPOINT = 'Auth/Admin/Login';
  private _user?: User | null;
  get loggedIn(): boolean {
    // const userJson = localStorage.getItem('SignedUser');
    // const userObject = userJson ? JSON.parse(userJson) : null;
    // this._user = userObject as User | null;
    // return!!this._user;

    return true;
  }

  private _lastAuthenticatedPath: string = defaultPath;
  set lastAuthenticatedPath(value: string) {
    this._lastAuthenticatedPath = value;
  }

  constructor(private apiClient: ApiClient, private router: Router) { }

  async logIn(payload: User) {
    let result= {
      isOk: false,
      data: "error...",
      message: "Authentication failed"
    };

    await firstValueFrom(this.apiClient.post(`${environment.BASE_URL}Auth/Admin/Login`, { email: payload.email, password: payload.password }))
      .then((data:any) => {
        debugger
        localStorage.setItem("SignedUser", JSON.stringify(data.connectedUser));
        localStorage.setItem("UserToken", JSON.stringify(data.token));
        result = {
          isOk: true,
          data: data,
          message: "Authentication successful"
        }
        this._user = data.connectedUser;
        this.router.navigate([this._lastAuthenticatedPath]);
      })
      .catch((err) => {
        result = {
          isOk: false,
          data: err,
          message: "Authentication failed"
        };
      });

      return result;
  }

  async getUser() {  
    return this.loggedIn
    ? {
      isOk: true,
      data: this._user
    }
    : {
      isOk: false,
      data: null
    };
  }
    

  async logOut() {
    this._user = null;
    localStorage.removeItem("SignedUser");
    localStorage.removeItem("UserToken");
    this.router.navigate(['/login']);
  }
}

@Injectable()
export class AuthGuardService implements CanActivate {
  constructor(private router: Router, private authService: AuthService) { }

  canActivate(route: ActivatedRouteSnapshot): boolean {
    const isLoggedIn = this.authService.loggedIn;
    const isAuthForm = [
      'login'
    ].includes(route.routeConfig?.path || defaultPath);

    if (isLoggedIn && isAuthForm) {
      this.authService.lastAuthenticatedPath = defaultPath;
      this.router.navigate([defaultPath]);
      return false;
    }

    if (!isLoggedIn && !isAuthForm) {
      this.router.navigate(['/login']);
    }

    if (isLoggedIn) {
      this.authService.lastAuthenticatedPath = route.routeConfig?.path || defaultPath;
    }

    return isLoggedIn || isAuthForm;
  }
}
