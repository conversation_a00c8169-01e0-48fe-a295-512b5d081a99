import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { EcoleService } from 'src/app/shared/services/ecoles/ecoles.service';

@Component({
  templateUrl: 'edit-ecole.component.html',
  styleUrls: [ './edit-ecole.component.scss' ]
})

export class EditEcoleComponent {
  public formData: any = {};
  public countries: any[] = [];
  public formations: any[] = [];
  public campuses: any[] = [];
  public programmes: any[] = [];
  public diplomes: any[] = [];
  public langues: any[] = [];
  public niveauxScolaires: any[] = [];

  constructor(private ecoleService: EcoleService, private router: Router) {}

  onCancel() {
    this.router.navigate(['/ecoles']);
  }

  submit() {
    // TODO: implement save logic
  }

  isFormValid(): boolean {
    // TODO: implement validation logic
    return true;
  }
}
