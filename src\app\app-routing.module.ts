import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { LoginFormComponent } from './shared/components';
import { AuthGuardService } from './shared/services';
import { HomeComponent } from './pages/home/<USER>';
import { DxButtonModule, DxDataGridModule, DxFormModule, DxLookupModule, DxSpeedDialActionModule, DxTabPanelModule, DxTagBoxModule, DxTextAreaModule } from 'devextreme-angular';
import { DxoEditingModule, DxoFormItemModule, DxoFormModule, DxoLookupModule } from 'devextreme-angular/ui/nested';
import { EcolesComponent } from './pages/ecoles/ecoles.component';
import { AddEcoleComponent } from './pages/add-ecole/add-ecole.component';
import { EditEcoleComponent } from './pages/edit-ecole/edit-ecole.component';

const routes: Routes = [
  {
    path: 'home',
    component: HomeComponent,
    canActivate: [ AuthGuardService ]
  },
  {
    path: 'ecoles',
    component: EcolesComponent,
    canActivate: [ AuthGuardService ]
  },
  {
    path: 'ecoles/add',
    component: AddEcoleComponent,
    canActivate: [ AuthGuardService ]
  },
  {
    path: 'ecoles/edit/:id',
    component: EditEcoleComponent,
    canActivate: [ AuthGuardService ]
  },
  {
    path: 'login',
    component: LoginFormComponent,
    canActivate: [ AuthGuardService ]
  },
  {
    path: '**',
    redirectTo: 'home'
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { useHash: false }),
      DxDataGridModule,
      DxFormModule,
      DxSpeedDialActionModule,
      DxTextAreaModule,
      DxButtonModule,
      DxLookupModule,
      DxoLookupModule,
      DxTagBoxModule,
      DxoEditingModule,
      DxoFormItemModule,
      DxoFormModule,
      DxTagBoxModule,
      DxDataGridModule,
      DxFormModule,
      DxTabPanelModule
    ],
  providers: [AuthGuardService],
  exports: [RouterModule],
  declarations: [
    HomeComponent,
    EcolesComponent,
    AddEcoleComponent,
    EditEcoleComponent,
  ]
})
export class AppRoutingModule { }
