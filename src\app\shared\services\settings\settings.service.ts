import { Injectable } from "@angular/core";
import { CanActivate, Router, ActivatedRouteSnapshot } from "@angular/router";
import { ApiClient } from "../api-client";
import { environment } from "src/environments/environment";

import { Observable } from "rxjs";
import { Setting } from "../ecoles/ecole.model";

@Injectable()
export class SettingsService {
  private MAIN_ENDPOINT = `${environment.BASE_URL}Settings/`;

  constructor(private http: ApiClient) {}

  getGenders(): Observable<Setting[]> {
    return this.http.get<Setting[]>(`${this.MAIN_ENDPOINT}Genders`);
  }

  getPays(): Observable<Setting[]> {
    return this.http.get<Setting[]>(`${this.MAIN_ENDPOINT}Pays`);
  }

  getVilles(): Observable<Setting[]> {
    return this.http.get<Setting[]>(`${this.MAIN_ENDPOINT}Villes`);
  }

  getLangues(): Observable<Setting[]> {
    return this.http.get<Setting[]>(`${this.MAIN_ENDPOINT}Langues`);
  }

  getNiveauLangues(): Observable<Setting[]> {
    return this.http.get<Setting[]>(`${this.MAIN_ENDPOINT}NiveauLangues`);
  }

  getDomaineEtudes(): Observable<Setting[]> {
    return this.http.get<Setting[]>(`${this.MAIN_ENDPOINT}DomaineEtudes`);
  }

  getNiveauScholaires(): Observable<Setting[]> {
    return this.http.get<Setting[]>(`${this.MAIN_ENDPOINT}NiveauScholaires`);
  }

  getDiplomes(): Observable<Setting[]> {
    return this.http.get<Setting[]>(`${this.MAIN_ENDPOINT}Diplomes`);
  }

  getEtablissements(): Observable<Setting[]> {
    return this.http.get<Setting[]>(`${this.MAIN_ENDPOINT}Etablissements`);
  }

  getMoyenneGenerales(): Observable<Setting[]> {
    return this.http.get<Setting[]>(`${this.MAIN_ENDPOINT}MoyenneGenerales`);
  }

  getSituationFinancieres(): Observable<Setting[]> {
    return this.http.get<Setting[]>(`${this.MAIN_ENDPOINT}SituationFinancieres`);
  }

  getExperiencePros(): Observable<Setting[]> {
    return this.http.get<Setting[]>(`${this.MAIN_ENDPOINT}ExperiencePros`);
  }
}
