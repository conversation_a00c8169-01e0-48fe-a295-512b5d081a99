@import "../../../../themes/generated/variables.base.scss";
@import "../../../../dx-styles.scss";

:host {
  flex: 0 0 auto;
  z-index: 1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);

  ::ng-deep .dx-toolbar .dx-toolbar-item.menu-button>.dx-toolbar-item-content .dx-icon {
    color: $base-accent;
  }
}

::ng-deep .dx-toolbar.header-toolbar .dx-toolbar-items-container .dx-toolbar-after {
  padding: 0 40px;

  :host-context(.screen-x-small) & {
    padding: 0 20px;
  }
}

::ng-deep .dx-toolbar .dx-toolbar-item.menu-button {
  width: $side-panel-min-width;
  text-align: center;
  padding: 0;
}

::ng-deep .header-title .dx-item-content {
  padding: 0;
  margin: 0;
}

:host-context(.dx-theme-generic) {
  .dx-toolbar {
    padding: 10px 0;
  }

  .user-button>.dx-button-content {
    padding: 3px;
  }
}
