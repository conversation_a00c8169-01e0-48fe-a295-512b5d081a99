import { Injectable } from '@angular/core';
import { firstValueFrom } from 'rxjs';

import { environment } from 'src/environments/environment';
import { ApiClient } from '../api-client';
import { Ecole } from './ecole.model';

const defaultPath = '/';

@Injectable()
export class EcoleService {
  private MAIN_ENDPOINT = `${environment.BASE_URL}Echoles/`;

  constructor(private apiClient: ApiClient) { }

  async GetEcoles(): Promise<any> {
    return await firstValueFrom(
      this.apiClient.get<any>(`${this.MAIN_ENDPOINT}GetAll`)
    );
  }

  async deleteEcole(id: number): Promise<any> {
    return await firstValueFrom(
      this.apiClient.delete<any>(`${this.MAIN_ENDPOINT}${id}`)
    );
  }

  async createEcole(payload: any): Promise<any> {
    return await firstValueFrom(
      this.apiClient.post<any>(`${this.MAIN_ENDPOINT}`, payload)
    );
  }
}
