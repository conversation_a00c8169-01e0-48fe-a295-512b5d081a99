.content-block {
  margin: 24px;
}

.header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  h2 {
    margin: 0;
  }

  .button-group {
    display: flex;
    gap: 12px;
  }
}

.dx-card {
  padding: 20px;
}

::ng-deep .dx-form {
//   max-width: 800px;
  margin: auto;
}

.campus-grid-container {
  margin-top: 24px;

  h4 {
    margin-bottom: 16px;
  }
}
.section-title{
    margin: 0;
}

.programme-grid-container {
  margin-top: 24px;
}
/* Image upload container styles */
.image-upload-container {
  width: 100%;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
  transition: border-color 0.3s ease;

  &:hover {
    border-color: #d1d1d1;
  }
}

/* Upload progress styles */
.upload-progress {
  width: 100%;
  text-align: center;
  
  p {
    margin: 8px 0 0 0;
    color: #666;
    font-size: 14px;
  }
}

/* Image preview styles */
.image-preview {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.preview-image {
  max-width: 100%;
  max-height: 200px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  object-fit: cover;
  
  &.logo-preview {
    max-height: 120px;
    max-width: 200px;
  }
  
  &.cover-preview {
    max-height: 150px;
    max-width: 300px;
  }
}

.image-actions {
  display: flex;
  gap: 8px;
}

/* DevExtreme file uploader customization */
.dx-fileuploader {
  width: 100%;
  
  .dx-fileuploader-content {
    border: none;
    background: transparent;
  }
  
  .dx-fileuploader-button {
    background: #1976d2;
    border-color: #1976d2;
    
    &:hover {
      background: #1565c0;
      border-color: #1565c0;
    }
  }
}

/* Progress bar customization */
.dx-progressbar {
  width: 100%;
  margin: 8px 0;
}

/* Form group spacing */
.dx-form-group {
  margin-bottom: 24px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .image-upload-container {
    min-height: 100px;
    padding: 12px;
  }
  
  .preview-image {
    &.logo-preview {
      max-height: 100px;
      max-width: 150px;
    }
    
    &.cover-preview {
      max-height: 120px;
      max-width: 250px;
    }
  }
}