{"name": "client-app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "build-themes": "devextreme build", "postinstall": "npm run build-themes"}, "private": true, "dependencies": {"@angular/animations": "^15.2.0", "@angular/cdk": "~15.2.0", "@angular/common": "^15.2.0", "@angular/compiler": "^15.2.0", "@angular/core": "^15.2.0", "@angular/forms": "^15.2.0", "@angular/platform-browser": "^15.2.0", "@angular/platform-browser-dynamic": "^15.2.0", "@angular/router": "^15.2.0", "devextreme": "^22.2.4", "devextreme-angular": "^22.2.4", "devextreme-schematics": "^1.5.1", "rxjs": "~7.8.0", "sweetalert2": "^11.10.2", "tslib": "^2.3.0", "zone.js": "~0.12.0"}, "devDependencies": {"@angular-devkit/build-angular": "^15.2.4", "@angular/cli": "~15.2.4", "@angular/compiler-cli": "^15.2.0", "@types/jasmine": "~4.3.0", "devextreme-cli": "latest", "devextreme-themebuilder": "^22.2.4", "jasmine-core": "~4.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "typescript": "~4.9.4"}}