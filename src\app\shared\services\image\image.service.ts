import { Injectable } from '@angular/core';
import { HttpClient, HttpEventType, HttpHeaders } from '@angular/common/http';
import { Observable, Subject } from 'rxjs';
import { environment } from 'src/environments/environment';

export interface ImageUploadResponse {
  url: string;
  id: string;
  fileName: string;
  size: number;
  type: string;
}

export interface ImgurUploadResponse {
  data: {
    id: string;
    title: string | null;
    description: string | null;
    datetime: number;
    type: string;
    animated: boolean;
    width: number;
    height: number;
    size: number;
    views: number;
    bandwidth: number;
    vote: string | null;
    favorite: boolean;
    nsfw: boolean | null;
    section: string | null;
    account_url: string | null;
    account_id: number | null;
    is_ad: boolean;
    in_most_viral: boolean;
    has_sound: boolean;
    tags: string[];
    ad_type: number;
    ad_url: string;
    edited: string;
    in_gallery: boolean;
    deletehash: string;
    name: string;
    link: string; // This is the direct URL to the image
  };
  success: boolean;
  status: number;
}

@Injectable({
  providedIn: 'root'
})
export class ImageService {
  private imgurApiUrl = 'https://api.imgur.com/3/image';
  private clientId = environment.IMGUR_CLIENT_ID;

  constructor(private http: HttpClient) {}

  /**
   * Upload image to Imgur with progress tracking
   * @param formData FormData containing the image file
   * @param onProgress Callback function for progress updates
   * @returns Promise with upload response
   */
  uploadImage(formData: FormData, onProgress?: (progress: number) => void): Promise<ImageUploadResponse> {
    return new Promise((resolve, reject) => {
      // Validate client ID
      if (!this.clientId || this.clientId === 'YOUR_IMGUR_CLIENT_ID') {
        reject(new Error('Imgur Client ID is not configured. Please set IMGUR_CLIENT_ID in environment.ts'));
        return;
      }

      // Set up headers for Imgur API
      const headers = new HttpHeaders({
        'Authorization': `Client-ID ${this.clientId}`
      });

      // Convert FormData to base64 for Imgur API
      const file = formData.get('image') as File;
      if (!file) {
        reject(new Error('No image file found in FormData'));
        return;
      }

      // Validate file before upload
      const validation = this.validateImageFile(file);
      if (!validation.isValid) {
        reject(new Error(validation.error));
        return;
      }

      // Convert file to base64
      const reader = new FileReader();
      reader.onload = () => {
        const base64 = (reader.result as string).split(',')[1]; // Remove data:image/...;base64, prefix

        // Create form data for Imgur API
        const imgurFormData = new FormData();
        imgurFormData.append('image', base64);
        imgurFormData.append('type', 'base64');

        const req = this.http.post<ImgurUploadResponse>(this.imgurApiUrl, imgurFormData, {
          headers,
          reportProgress: true,
          observe: 'events'
        });

        req.subscribe({
          next: (event) => {
            if (event.type === HttpEventType.UploadProgress) {
              // Calculate progress percentage
              const progress = Math.round(100 * event.loaded / (event.total || 1));
              if (onProgress) {
                onProgress(progress);
              }
            } else if (event.type === HttpEventType.Response) {
              // Upload completed - transform Imgur response to our format
              const imgurResponse = event.body as ImgurUploadResponse;
              if (imgurResponse.success && imgurResponse.data) {
                const response: ImageUploadResponse = {
                  url: imgurResponse.data.link,
                  id: imgurResponse.data.id,
                  fileName: imgurResponse.data.name || file.name,
                  size: imgurResponse.data.size,
                  type: imgurResponse.data.type
                };
                resolve(response);
              } else {
                reject(new Error('Failed to upload image to Imgur'));
              }
            }
          },
          error: (error) => {
            console.error('Imgur upload error:', error);
            reject(error);
          }
        });
      };

      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };

      reader.readAsDataURL(file);
    });
  }

  /**
   * Alternative upload method with Observable for more control
   * @param formData FormData containing the image file
   * @returns Observable with upload events
   */
  uploadImageObservable(formData: FormData): Observable<any> {
    // For now, convert the Promise-based method to Observable
    return new Observable(observer => {
      this.uploadImage(formData, (progress) => {
        observer.next({ type: 'progress', progress });
      }).then(response => {
        observer.next({ type: 'response', response });
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  /**
   * Delete image from Imgur by delete hash
   * Note: Imgur requires the delete hash, not the image ID
   * @param deleteHash Delete hash provided during upload
   * @returns Observable with deletion response
   */
  deleteImage(deleteHash: string): Observable<any> {
    if (!this.clientId || this.clientId === 'YOUR_IMGUR_CLIENT_ID') {
      throw new Error('Imgur Client ID is not configured. Please set IMGUR_CLIENT_ID in environment.ts');
    }

    const headers = new HttpHeaders({
      'Authorization': `Client-ID ${this.clientId}`
    });

    return this.http.delete(`https://api.imgur.com/3/image/${deleteHash}`, { headers });
  }

  /**
   * Get image information from Imgur by ID
   * @param imageId Image ID
   * @returns Observable with image data
   */
  getImage(imageId: string): Observable<any> {
    if (!this.clientId || this.clientId === 'YOUR_IMGUR_CLIENT_ID') {
      throw new Error('Imgur Client ID is not configured. Please set IMGUR_CLIENT_ID in environment.ts');
    }

    const headers = new HttpHeaders({
      'Authorization': `Client-ID ${this.clientId}`
    });

    return this.http.get(`https://api.imgur.com/3/image/${imageId}`, { headers });
  }

  /**
   * Note: Imgur doesn't provide a way to get all images for anonymous uploads
   * This method is kept for interface compatibility but will throw an error
   * @returns Observable with array of images
   */
  getAllImages(): Observable<ImageUploadResponse[]> {
    throw new Error('Getting all images is not supported with Imgur anonymous uploads. Consider using Imgur account-based uploads if you need this functionality.');
  }

  /**
   * Validate image file before upload to Imgur
   * @param file File to validate
   * @returns Validation result
   */
  validateImageFile(file: File): { isValid: boolean; error?: string } {
    const maxSize = 10 * 1024 * 1024; // 10MB (Imgur's limit)
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp', 'image/tiff'];

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Invalid file type. Only JPEG, PNG, GIF, WebP, BMP, and TIFF images are allowed.'
      };
    }

    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File size too large. Maximum size is 10MB for Imgur uploads.'
      };
    }

    return { isValid: true };
  }

  /**
   * Resize image before upload (optional utility)
   * @param file Original file
   * @param maxWidth Maximum width
   * @param maxHeight Maximum height
   * @param quality Image quality (0-1)
   * @returns Promise with resized file
   */
  resizeImage(file: File, maxWidth: number, maxHeight: number, quality: number = 0.8): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        
        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob((blob) => {
          const resizedFile = new File([blob!], file.name, {
            type: file.type,
            lastModified: Date.now()
          });
          resolve(resizedFile);
        }, file.type, quality);
      };

      img.src = URL.createObjectURL(file);
    });
  }
}