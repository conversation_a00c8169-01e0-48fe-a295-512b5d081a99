import { Injectable } from '@angular/core';
import { HttpClient, HttpEventType, HttpHeaders } from '@angular/common/http';
import { Observable, Subject } from 'rxjs';
import { environment } from 'src/environments/environment';

export interface ImageUploadResponse {
  url: string;
  id: string;
  fileName: string;
  size: number;
  type: string;
}

@Injectable({
  providedIn: 'root'
})
export class ImageService {
  private baseUrl = environment.apiUrl || 'http://localhost:3000/api'; // Adjust based on your API

  constructor(private http: HttpClient) {}

  /**
   * Upload image with progress tracking
   * @param formData FormData containing the image file
   * @param onProgress Callback function for progress updates
   * @returns Promise with upload response
   */
  uploadImage(formData: FormData, onProgress?: (progress: number) => void): Promise<ImageUploadResponse> {
    return new Promise((resolve, reject) => {
      const req = this.http.post<ImageUploadResponse>(`${this.baseUrl}/images/upload`, formData, {
        reportProgress: true,
        observe: 'events'
      });

      req.subscribe({
        next: (event) => {
          if (event.type === HttpEventType.UploadProgress) {
            // Calculate progress percentage
            const progress = Math.round(100 * event.loaded / (event.total || 1));
            if (onProgress) {
              onProgress(progress);
            }
          } else if (event.type === HttpEventType.Response) {
            // Upload completed
            resolve(event.body as ImageUploadResponse);
          }
        },
        error: (error) => {
          reject(error);
        }
      });
    });
  }

  /**
   * Alternative upload method with Observable for more control
   * @param formData FormData containing the image file
   * @returns Observable with upload events
   */
  uploadImageObservable(formData: FormData): Observable<any> {
    return this.http.post<ImageUploadResponse>(`${this.baseUrl}/images/upload`, formData, {
      reportProgress: true,
      observe: 'events'
    });
  }

  /**
   * Delete image by URL or ID
   * @param imageId Image ID or URL to delete
   * @returns Observable with deletion response
   */
  deleteImage(imageId: string): Observable<any> {
    return this.http.delete(`${this.baseUrl}/images/${imageId}`);
  }

  /**
   * Get image by ID
   * @param imageId Image ID
   * @returns Observable with image data
   */
  getImage(imageId: string): Observable<ImageUploadResponse> {
    return this.http.get<ImageUploadResponse>(`${this.baseUrl}/images/${imageId}`);
  }

  /**
   * Get all images (for admin/management purposes)
   * @returns Observable with array of images
   */
  getAllImages(): Observable<ImageUploadResponse[]> {
    return this.http.get<ImageUploadResponse[]>(`${this.baseUrl}/images`);
  }

  /**
   * Validate image file before upload
   * @param file File to validate
   * @returns Validation result
   */
  validateImageFile(file: File): { isValid: boolean; error?: string } {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Invalid file type. Only JPEG, PNG, GIF, and WebP images are allowed.'
      };
    }

    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File size too large. Maximum size is 5MB.'
      };
    }

    return { isValid: true };
  }

  /**
   * Resize image before upload (optional utility)
   * @param file Original file
   * @param maxWidth Maximum width
   * @param maxHeight Maximum height
   * @param quality Image quality (0-1)
   * @returns Promise with resized file
   */
  resizeImage(file: File, maxWidth: number, maxHeight: number, quality: number = 0.8): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        
        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob((blob) => {
          const resizedFile = new File([blob!], file.name, {
            type: file.type,
            lastModified: Date.now()
          });
          resolve(resizedFile);
        }, file.type, quality);
      };

      img.src = URL.createObjectURL(file);
    });
  }
}