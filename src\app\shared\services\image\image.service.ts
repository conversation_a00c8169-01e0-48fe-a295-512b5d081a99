import { Injectable } from '@angular/core';
import { HttpClient, HttpEventType, HttpHeaders } from '@angular/common/http';
import { Observable, Subject } from 'rxjs';
import { environment } from 'src/environments/environment';

export interface ImageUploadResponse {
  url: string;
  id: string;
  fileName: string;
  size: number;
  type: string;
}

export interface ImgBBUploadResponse {
  data: {
    id: string;
    title: string;
    url_viewer: string;
    url: string;
    display_url: string;
    width: string;
    height: string;
    size: string;
    time: string;
    expiration: string;
    image: {
      filename: string;
      name: string;
      mime: string;
      extension: string;
      url: string;
    };
    thumb: {
      filename: string;
      name: string;
      mime: string;
      extension: string;
      url: string;
    };
    medium: {
      filename: string;
      name: string;
      mime: string;
      extension: string;
      url: string;
    };
    delete_url: string;
  };
  success: boolean;
  status: number;
}

@Injectable({
  providedIn: 'root'
})
export class ImageService {
  private imgbbApiUrl = 'https://api.imgbb.com/1/upload';
  private apiKey = environment.IMGBB_API_KEY;

  constructor(private http: HttpClient) {}

  /**
   * Upload image to ImgBB with progress tracking
   * @param formData FormData containing the image file
   * @param onProgress Callback function for progress updates
   * @returns Promise with upload response
   */
  uploadImage(formData: FormData, onProgress?: (progress: number) => void): Promise<ImageUploadResponse> {
    console.log('ImageService.uploadImage called');
    return new Promise((resolve, reject) => {
      // Validate API key
      if (!this.apiKey) {
        console.error('ImgBB API key not configured');
        reject(new Error('ImgBB API key is not configured. Please set IMGBB_API_KEY in environment.ts'));
        return;
      }
      console.log('API key validated:', this.apiKey.substring(0, 8) + '...');

      // Get the file from FormData
      const file = formData.get('image') as File;
      if (!file) {
        console.error('No file found in FormData');
        reject(new Error('No image file found in FormData'));
        return;
      }
      console.log('File extracted from FormData:', file.name, file.size, file.type);

      // Validate file before upload
      const validation = this.validateImageFile(file);
      if (!validation.isValid) {
        reject(new Error(validation.error));
        return;
      }

      // Create form data for ImgBB API
      const imgbbFormData = new FormData();
      imgbbFormData.append('image', file);

      // ImgBB expects the API key as a URL parameter
      const uploadUrl = `${this.imgbbApiUrl}?key=${this.apiKey}`;
      console.log('Upload URL:', uploadUrl);

      const req = this.http.post<ImgBBUploadResponse>(uploadUrl, imgbbFormData, {
        reportProgress: true,
        observe: 'events'
      });

      console.log('HTTP request created, subscribing...');
      req.subscribe({
        next: (event) => {
          console.log('HTTP event received:', event.type, event);
          if (event.type === HttpEventType.UploadProgress) {
            // Calculate progress percentage
            const progress = Math.round(100 * event.loaded / (event.total || 1));
            console.log('Upload progress:', progress + '%');
            if (onProgress) {
              onProgress(progress);
            }
          } else if (event.type === HttpEventType.Response) {
            // Upload completed - transform ImgBB response to our format
            console.log('Upload completed, response:', event.body);
            const imgbbResponse = event.body as ImgBBUploadResponse;
            if (imgbbResponse.success && imgbbResponse.data) {
              const response: ImageUploadResponse = {
                url: imgbbResponse.data.url,
                id: imgbbResponse.data.id,
                fileName: imgbbResponse.data.image.filename,
                size: parseInt(imgbbResponse.data.size),
                type: imgbbResponse.data.image.mime
              };
              console.log('Transformed response:', response);
              resolve(response);
            } else {
              console.error('ImgBB response indicates failure:', imgbbResponse);
              reject(new Error('Failed to upload image to ImgBB'));
            }
          }
        },
        error: (error) => {
          console.error('ImgBB upload error:', error);
          reject(error);
        }
      });
    });
  }

  /**
   * Alternative upload method with Observable for more control
   * @param formData FormData containing the image file
   * @returns Observable with upload events
   */
  uploadImageObservable(formData: FormData): Observable<any> {
    // For now, convert the Promise-based method to Observable
    return new Observable(observer => {
      this.uploadImage(formData, (progress) => {
        observer.next({ type: 'progress', progress });
      }).then(response => {
        observer.next({ type: 'response', response });
        observer.complete();
      }).catch(error => {
        observer.error(error);
      });
    });
  }

  /**
   * Delete image from ImgBB using delete URL
   * Note: ImgBB provides a delete_url in the upload response
   * @param deleteUrl Delete URL provided during upload
   * @returns Observable with deletion response
   */
  deleteImage(deleteUrl: string): Observable<any> {
    // ImgBB deletion is done via the delete_url provided in upload response
    // This is a simple GET request to the delete URL
    return this.http.get(deleteUrl);
  }

  /**
   * Get image information from ImgBB by ID
   * Note: ImgBB doesn't provide a direct API to get image info by ID
   * This method is kept for interface compatibility but will throw an error
   * @param imageId Image ID
   * @returns Observable with image data
   */
  getImage(imageId: string): Observable<any> {
    throw new Error('Getting image information by ID is not supported by ImgBB API. Use the direct image URL instead.');
  }

  /**
   * Note: ImgBB doesn't provide a way to get all images for anonymous uploads
   * This method is kept for interface compatibility but will throw an error
   * @returns Observable with array of images
   */
  getAllImages(): Observable<ImageUploadResponse[]> {
    throw new Error('Getting all images is not supported with ImgBB anonymous uploads. Consider using ImgBB account-based uploads if you need this functionality.');
  }

  /**
   * Validate image file before upload to ImgBB
   * @param file File to validate
   * @returns Validation result
   */
  validateImageFile(file: File): { isValid: boolean; error?: string } {
    const maxSize = 32 * 1024 * 1024; // 32MB (ImgBB's limit)
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/bmp', 'image/tiff'];

    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: 'Invalid file type. Only JPEG, PNG, GIF, WebP, BMP, and TIFF images are allowed.'
      };
    }

    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File size too large. Maximum size is 32MB for ImgBB uploads.'
      };
    }

    return { isValid: true };
  }

  /**
   * Resize image before upload (optional utility)
   * @param file Original file
   * @param maxWidth Maximum width
   * @param maxHeight Maximum height
   * @param quality Image quality (0-1)
   * @returns Promise with resized file
   */
  resizeImage(file: File, maxWidth: number, maxHeight: number, quality: number = 0.8): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        
        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob((blob) => {
          const resizedFile = new File([blob!], file.name, {
            type: file.type,
            lastModified: Date.now()
          });
          resolve(resizedFile);
        }, file.type, quality);
      };

      img.src = URL.createObjectURL(file);
    });
  }
}