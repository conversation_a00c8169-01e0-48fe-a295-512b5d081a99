<?xml version="1.0"?>
<configuration>
    <system.webServer>
		<rewrite>
          <rules>
            <rule name="AngularJS Routes" stopProcessing="true">
              <match url=".*" />
              <conditions logicalGrouping="MatchAll">
                <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />   
              </conditions>
              <action type="Rewrite" url="/" />
            </rule>
			<rule name="RedirectWwwToNonWww" stopProcessing="false">
			  <match url="(.*)" />
			  <conditions logicalGrouping="MatchAll" trackAllCaptures="false">
				<add input="{HTTP_HOST}" pattern="^(www\.)(.*)$" />
			  </conditions>
			  <action type="Redirect" url="https://{C:2}{REQUEST_URI}" redirectType="Permanent" />
			</rule>
          </rules>
        </rewrite>
        <staticContent>
            <mimeMap fileExtension=".json" mimeType="application/json" />
            <mimeMap fileExtension=".jpeg" mimeType="image/jpeg"/>
            <mimeMap fileExtension=".webp" mimeType="image/webp"/>
            <mimeMap fileExtension=".jpg" mimeType="image/jpeg"/>
            <mimeMap fileExtension=".gif" mimeType="image/gif"/>
            <mimeMap fileExtension=".png" mimeType="image/png"/>
            <mimeMap fileExtension=".bmp" mimeType="image/bmp"/>
            <mimeMap fileExtension=".svg" mimeType="image/svg+xml"/>
            <mimeMap fileExtension=".svgz" mimeType="image/svg+xml"/>
            <mimeMap fileExtension=".eot" mimeType="application/vnd.ms-fontobject"/>
            <mimeMap fileExtension=".otf" mimeType="font/otf"/>
            <mimeMap fileExtension=".woff" mimeType="font/x-woff"/>
        </staticContent>
		
		<httpProtocol>
		  <customHeaders>
			<remove name="X-Powered-By" />
		  </customHeaders>
		</httpProtocol>
		<security>
		  <requestFiltering removeServerHeader="true" />
		</security>
    </system.webServer>
</configuration> 