import { Component } from '@angular/core';
import { Router } from '@angular/router';
import CustomStore from 'devextreme/data/custom_store';
import notify from 'devextreme/ui/notify';
import { EcoleService } from 'src/app/shared/services/ecoles/ecoles.service';
import Swal from 'sweetalert2';

@Component({
  templateUrl: 'ecoles.component.html',
  styleUrls: [ './ecoles.component.scss' ]
})

export class EcolesComponent {
  // ecoles?: any;
  // dataSource: any;

  
  ecoles: any[] = [
    { id: 1, nom: 'Ecole Polytechnique', pays: 'France', isVerified: true },
    { id: 2, nom: 'Université Mohammed V', pays: 'Morocco', isVerified: false },
    { id: 3, nom: 'Harvard University', pays: 'USA', isVerified: true }
  ];  dataSource: any = this.ecoles;
  states: any;
  selectedRowData: any = null;

  constructor(
    private ecoleService: EcoleService,
    private router: Router
  ) {
    // this.dataSource = new CustomStore({
    //   key: 'id',
    //   load: async () =>  await this.ecoleService.GetEcoles().then((result) => result)
    // });
  }

  onAdd() {
    this.router.navigate(['/ecoles/add']);
  }

  onEdit() {
    // Implement edit logic
  }

  async onDelete() {
    if (!this.selectedRowData) {
      notify('Please select a row to delete', 'error');
      return;
    }

    const result = await Swal.fire({
      title: 'Are you sure?',
      text: `Do you want to delete ${this.selectedRowData.nom}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel',
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6'
    });

    if (result.isConfirmed) {
      // await this.ecoleService.deleteEcole(this.selectedRowData.id)
      // .then(() => {
      //   notify('Ecole deleted successfully', 'success');
      //   this.dataSource = new CustomStore({
      //     key: 'id',
      //     load: async () =>  await this.ecoleService.GetEcoles().then((result) => result)
      //   });
      //   this.selectedRowData = null;
      // }).catch((error) => {
      //   notify('Error deleting ecole', 'error');
      //   console.error('Delete error:', error);
      // });

      this.ecoles = this.ecoles.filter(e => e.id !== this.selectedRowData.id);
      this.dataSource = this.ecoles;
      notify('Ecole deleted successfully', 'success');
      this.selectedRowData = null;
    }
  }

  onSelectionChanged(e: any) {
    this.selectedRowData = e.selectedRowsData[0];
  }

  onRowClick(e: any) {
    if (e && e.data && e.data.id) {
      this.router.navigate(['/ecoles/edit', e.data.id]);
    }
  }
}
