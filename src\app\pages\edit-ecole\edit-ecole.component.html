<div class="content-block">
  <div class="header-row">
    <h2>Modifier Ecole</h2>
    <div class="button-group">
      <dx-button
        text="Cancel"
        type="normal"
        [width]="120"
        (onClick)="onCancel()"
      >
      </dx-button>
      <dx-button
        text="Save"
        type="default"
        [width]="120"
        (onClick)="submit()"
        [disabled]="!isFormValid()"
      >
      </dx-button>
    </div>
  </div>

  <div class="dx-card responsive-paddings">
    <h5 class="section-title">Details</h5>
    <dx-form
      id="form"
      [formData]="formData"
      [colCount]="2"
      [showColonAfterLabel]="true"
      [showValidationSummary]="true"
      validationGroup="customerData"
    >
      <dxi-item dataField="nom" [isRequired]="true">
        <dxi-validation-rule type="required" message="Nom is required">
        </dxi-validation-rule>
      </dxi-item>

      <dxi-item dataField="subtitle"> </dxi-item>

      <dxi-item
        dataField="pays"
        editorType="dxSelectBox"
        [isRequired]="true"
        [editorOptions]="{
          dataSource: countries,
          valueExpr: 'value',
          displayExpr: 'text'
        }"
      >
      </dxi-item>

      <dxi-item dataField="addresse"> </dxi-item>

      <dxi-item dataField="logoUrl"> </dxi-item>

      <dxi-item dataField="coverUrl"> </dxi-item>

      <dxi-item
        colSpan="2"
        dataField="ecoleFormations"
        editorType="dxTagBox"
        [editorOptions]="{
          dataSource: formations,
          valueExpr: 'value',
          displayExpr: 'text',
          showSelectionControls: true,
          applyValueMode: 'useButtons',
          placeholder: 'Select formations...'
        }"
      >
        <dxi-validation-rule
          type="required"
          message="At least one formation is required"
        >
        </dxi-validation-rule>
      </dxi-item>

      <dxi-item
        colSpan="2"
        dataField="presentation"
        editorType="dxTextArea"
        [editorOptions]="{ height: 90 }"
      >
      </dxi-item>

      <dxi-item dataField="isVerified" editorType="dxCheckBox"> </dxi-item>

      <dxi-item dataField="isfirstChoice" editorType="dxCheckBox"> </dxi-item>
    </dx-form>
  </div>

  <!-- Campus Section -->
  <div class="dx-card responsive-paddings campus-grid-container">
    <h5 class="section-title">Campus</h5>
    <dx-data-grid
      [dataSource]="campuses"
      [showBorders]="true"
      [columnAutoWidth]="true"
    >
      <dxo-editing
        mode="row"
        [allowAdding]="true"
        [allowUpdating]="true"
        [allowDeleting]="true"
      >
      </dxo-editing>

      <dxi-column
        dataField="nom"
        caption="Nom"
        [validationRules]="[{ type: 'required' }]"
      >
      </dxi-column>

      <dxi-column dataField="pays" caption="Pays" [validationRules]="[{ type: 'required' }]">
        <dxo-lookup
          [dataSource]="countries"
          valueExpr="value"
          displayExpr="text"
        >
        </dxo-lookup>
      </dxi-column>

      <dxi-column dataField="addresse" caption="Addresse" [validationRules]="[{ type: 'required' }]"> </dxi-column>
    </dx-data-grid>
  </div>
  <!-- End Campus Section -->

  <!-- Programmes Section -->
  <div class="dx-card responsive-paddings programme-grid-container">
    <h5 class="section-title">Programmes</h5>
    <dx-data-grid
      [dataSource]="programmes"
      [showBorders]="true"
      [columnAutoWidth]="true"
    >
      <dxo-editing
        mode="row"
        [allowAdding]="true"
        [allowUpdating]="true"
        [allowDeleting]="true"
      >
      </dxo-editing>

      <dxi-column
        dataField="nom"
        caption="Nom"
        [validationRules]="[{ type: 'required' }]"
      >
      </dxi-column>

      <dxi-column dataField="diplome" caption="Diplome" [validationRules]="[{ type: 'required' }]">
        <dxo-lookup
          [dataSource]="diplomes"
          valueExpr="value"
          displayExpr="text"
        >
        </dxo-lookup>
      </dxi-column>

      <dxi-column dataField="langue" caption="Langue" [validationRules]="[{ type: 'required' }]">
        <dxo-lookup [dataSource]="langues" valueExpr="value" displayExpr="text">
        </dxo-lookup>
      </dxi-column>

      <dxi-column dataField="niveauAdmission" caption="Niveaux d'admission" [validationRules]="[{ type: 'required' }]">
        <dxo-lookup
          [dataSource]="niveauxScolaires"
          valueExpr="value"
          displayExpr="text"
          selectionMode="multiple"
        >
        </dxo-lookup>
      </dxi-column>

      <dxi-column dataField="duree" caption="Durée"> </dxi-column>
      <dxi-column dataField="fraisInscription" caption="Frais d'inscription"> </dxi-column>
      <dxi-column dataField="fraisScholarite" caption="Frais de scolarité"> </dxi-column>
      <dxi-column dataField="dateEntree" caption="Date d'entrée"> </dxi-column>

     
    </dx-data-grid>
  </div>
  <!-- End Programmes Section -->
</div>

