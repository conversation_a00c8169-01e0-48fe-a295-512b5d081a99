import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { AppComponent } from './app.component';
import { SideNavOuterToolbarModule, SideNavInnerToolbarModule, SingleCardModule } from './layouts';
import { FooterModule, LoginFormModule } from './shared/components';
import { UnauthenticatedContentModule } from './unauthenticated-content';
import { AppRoutingModule } from './app-routing.module';
import { HttpClientModule } from '@angular/common/http';
import { ApiClient } from './shared/services/api-client';

import { Component, enableProdMode } from '@angular/core';
import { BrowserTransferStateModule } from '@angular/platform-browser';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { DxDataGridModule } from 'devextreme-angular';

import { ScreenService, AppInfoService, AuthService, EcoleService } from './shared/services';
import { SettingsService } from './shared/services/settings/settings.service';
@NgModule({
  declarations: [
    AppComponent
  ],
  imports: [
    BrowserModule,
    SideNavOuterToolbarModule,
    SideNavInnerToolbarModule,
    SingleCardModule,
    FooterModule,
    LoginFormModule,
    UnauthenticatedContentModule,
    AppRoutingModule,
    HttpClientModule,
    BrowserTransferStateModule,
    DxDataGridModule,
  ],
  providers: [
    ApiClient,
    ScreenService,
    AppInfoService,
    AuthService,
    EcoleService,
    SettingsService
  ],
  bootstrap: [AppComponent]
})
export class AppModule { }
